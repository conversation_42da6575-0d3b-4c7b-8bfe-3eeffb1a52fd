"""
Excel模板预生成服务
负责在服务启动时批量生成所有Excel模板，提高用户请求响应速度
"""

import asyncio
import hashlib
import json
import time
from pathlib import Path

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import RuleTemplate
from services.excel_template_service import ExcelTemplateService
from services.rule_detail_service import ServiceError


class TemplateVersionManager:
    """模板版本管理器"""

    def __init__(self, version_file: str = "data/template_versions.json"):
        """
        初始化版本管理器

        Args:
            version_file: 版本记录文件路径
        """
        self.version_file = Path(version_file)
        self.version_file.parent.mkdir(exist_ok=True)
        self.versions = self._load_versions()

    def _load_versions(self) -> dict[str, str]:
        """加载版本记录"""
        try:
            if self.version_file.exists():
                with open(self.version_file, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载版本记录失败: {e}")
        return {}

    def _save_versions(self):
        """保存版本记录"""
        try:
            with open(self.version_file, "w", encoding="utf-8") as f:
                json.dump(self.versions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存版本记录失败: {e}")

    def calculate_template_version(self, template: RuleTemplate) -> str:
        """
        计算模板版本（基于元数据hash）

        Args:
            template: 规则模板对象

        Returns:
            str: 版本hash值
        """
        try:
            # 获取字段元数据
            field_metadata = template.get_field_metadata_list()

            # 构建版本数据
            version_data = {
                "rule_key": template.rule_key,
                "rule_name": template.name,
                "updated_at": template.updated_at.isoformat() if template.updated_at else None,
                "fields": [
                    {
                        "field_name": fm.field_name,
                        "field_type": fm.field_type.value if hasattr(fm.field_type, 'value') else fm.field_type,
                        "is_required": fm.is_required,
                        "display_name": fm.display_name,
                        "excel_column_order": fm.excel_column_order,
                        "validation_rule": fm.validation_rule,
                    }
                    for fm in sorted(field_metadata, key=lambda x: x.field_name)
                ],
            }

            # 计算hash
            version_str = json.dumps(version_data, sort_keys=True, ensure_ascii=False)
            return hashlib.md5(version_str.encode("utf-8")).hexdigest()[:8]

        except Exception as e:
            logger.error(f"计算模板版本失败: rule_key={template.rule_key}, error={e}")
            # 返回基于时间戳的fallback版本
            return hashlib.md5(f"{template.rule_key}_{time.time()}".encode()).hexdigest()[:8]

    def get_current_version(self, rule_key: str) -> str | None:
        """获取当前记录的版本"""
        return self.versions.get(rule_key)

    def update_version_record(self, rule_key: str, version: str):
        """更新版本记录"""
        self.versions[rule_key] = version
        self._save_versions()
        logger.debug(f"更新版本记录: {rule_key} -> {version}")

    def is_version_changed(self, template: RuleTemplate) -> bool:
        """检查版本是否发生变化"""
        current_version = self.calculate_template_version(template)
        recorded_version = self.get_current_version(template.rule_key)
        return current_version != recorded_version


class TemplateFileManager:
    """模板文件管理器"""

    def __init__(self, base_dir: str = "generated_templates"):
        """
        初始化文件管理器

        Args:
            base_dir: 模板文件存储目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)

    def get_standard_path(self, rule_key: str, version: str) -> Path:
        """
        获取标准文件路径

        Args:
            rule_key: 规则键
            version: 版本号

        Returns:
            Path: 标准文件路径
        """
        filename = f"{rule_key}_{version}.xlsx"
        return self.base_dir / filename

    def get_template_file(self, rule_key: str, version: str = None) -> Path | None:
        """
        获取模板文件路径

        Args:
            rule_key: 规则键
            version: 版本号（可选）

        Returns:
            Optional[Path]: 文件路径，如果不存在返回None
        """
        if version:
            file_path = self.get_standard_path(rule_key, version)
            return file_path if file_path.exists() else None

        # 如果没有指定版本，查找最新版本
        pattern = f"{rule_key}_*.xlsx"
        files = list(self.base_dir.glob(pattern))
        if files:
            # 按修改时间排序，返回最新的
            latest_file = max(files, key=lambda f: f.stat().st_mtime)
            return latest_file

        return None

    def cleanup_old_versions(self, rule_key: str, current_version: str):
        """
        清理旧版本文件

        Args:
            rule_key: 规则键
            current_version: 当前版本
        """
        pattern = f"{rule_key}_*.xlsx"
        files = list(self.base_dir.glob(pattern))

        cleaned_count = 0
        for file in files:
            if not file.name.endswith(f"_{current_version}.xlsx"):
                try:
                    file.unlink()
                    cleaned_count += 1
                    logger.debug(f"删除旧版本模板: {file}")
                except Exception as e:
                    logger.warning(f"删除文件失败: {file}, error: {e}")

        if cleaned_count > 0:
            logger.info(f"清理旧版本文件: rule_key={rule_key}, count={cleaned_count}")

    def get_all_template_files(self) -> list[dict[str, any]]:
        """
        获取所有模板文件信息

        Returns:
            List[Dict]: 文件信息列表
        """
        files = []
        for file_path in self.base_dir.glob("*.xlsx"):
            try:
                # 解析文件名
                name_parts = file_path.stem.split("_")
                if len(name_parts) >= 2:
                    rule_key = "_".join(name_parts[:-1])
                    version = name_parts[-1]
                else:
                    rule_key = file_path.stem
                    version = "unknown"

                stat = file_path.stat()
                files.append(
                    {
                        "rule_key": rule_key,
                        "version": version,
                        "file_path": str(file_path),
                        "file_size": stat.st_size,
                        "last_modified": stat.st_mtime,
                        "exists": True,
                    }
                )
            except Exception as e:
                logger.warning(f"解析文件信息失败: {file_path}, error: {e}")

        return files


class TemplatePreGenerationService:
    """模板预生成服务 - 服务启动时批量生成所有模板"""

    def __init__(self, session: Session, output_dir: str = "generated_templates"):
        """
        初始化服务

        Args:
            session: 数据库会话
            output_dir: 模板输出目录
        """
        self.session = session
        self.output_dir = output_dir
        self.version_manager = TemplateVersionManager()
        self.file_manager = TemplateFileManager(output_dir)
        self.excel_service = ExcelTemplateService(session, output_dir)

    async def generate_all_templates_on_startup(self) -> dict[str, any]:
        """
        服务启动时生成所有模板

        Returns:
            Dict: 生成结果统计
        """
        start_time = time.time()
        logger.info("开始预生成所有Excel模板...")

        try:
            # 1. 获取所有规则模板
            templates = self.session.query(RuleTemplate).all()
            logger.info(f"发现 {len(templates)} 个规则模板")

            # 2. 批量生成
            results = {"total": len(templates), "generated": 0, "skipped": 0, "failed": 0, "details": []}

            # 使用信号量限制并发数
            semaphore = asyncio.Semaphore(3)  # 最多3个并发生成

            async def generate_with_semaphore(template):
                async with semaphore:
                    return await self._generate_template_if_needed(template)

            # 并发生成所有模板
            tasks = [generate_with_semaphore(template) for template in templates]
            generation_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            for i, result in enumerate(generation_results):
                template = templates[i]
                if isinstance(result, Exception):
                    results["failed"] += 1
                    results["details"].append({"rule_key": template.rule_key, "status": "failed", "error": str(result)})
                    logger.error(f"生成模板失败: {template.rule_key}, error: {result}")
                elif result:
                    results["generated"] += 1
                    results["details"].append({"rule_key": template.rule_key, "status": "generated"})
                else:
                    results["skipped"] += 1
                    results["details"].append({"rule_key": template.rule_key, "status": "skipped"})

            # 记录总结
            duration = time.time() - start_time
            logger.info(
                f"模板预生成完成: 总数={results['total']}, "
                f"生成={results['generated']}, 跳过={results['skipped']}, "
                f"失败={results['failed']}, 耗时={duration:.2f}s"
            )

            results["duration"] = duration
            return results

        except Exception as e:
            error_msg = f"批量生成模板失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ServiceError(error_msg, error_code="BATCH_GENERATION_FAILED") from e

    async def _generate_template_if_needed(self, template: RuleTemplate) -> bool:
        """
        检查并生成模板（如果需要）

        Args:
            template: 规则模板对象

        Returns:
            bool: 是否生成了新模板
        """
        try:
            # 1. 计算当前版本
            current_version = self.version_manager.calculate_template_version(template)

            # 2. 检查文件是否存在且版本匹配
            existing_file = self.file_manager.get_template_file(template.rule_key, current_version)
            if existing_file and existing_file.exists():
                logger.debug(f"模板已存在，跳过生成: {template.rule_key}")
                return False

            # 3. 检查是否有字段元数据
            field_metadata = template.get_field_metadata_list()
            if not field_metadata:
                logger.warning(f"规则模板缺少字段元数据，跳过生成: {template.rule_key}")
                return False

            # 4. 生成新模板
            logger.info(f"生成新模板: {template.rule_key}")
            file_path = await asyncio.to_thread(self.excel_service.generate_template_by_rule_key, template.rule_key)

            # 5. 移动到标准位置
            standard_path = self.file_manager.get_standard_path(template.rule_key, current_version)
            if file_path != standard_path:
                file_path.rename(standard_path)
                logger.debug(f"移动文件到标准位置: {file_path} -> {standard_path}")

            # 6. 更新版本记录
            self.version_manager.update_version_record(template.rule_key, current_version)

            # 7. 清理旧版本文件
            self.file_manager.cleanup_old_versions(template.rule_key, current_version)

            logger.info(f"模板生成成功: {template.rule_key}, version: {current_version}")
            return True

        except Exception as e:
            error_msg = f"生成模板失败: rule_key={template.rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="TEMPLATE_GENERATION_FAILED", details={"rule_key": template.rule_key}
            ) from e

    async def regenerate_template(self, rule_key: str) -> bool:
        """
        重新生成指定模板

        Args:
            rule_key: 规则键

        Returns:
            bool: 是否成功生成
        """
        try:
            # 获取规则模板
            template = self.session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()

            if not template:
                raise ServiceError(
                    f"规则模板不存在: {rule_key}", error_code="TEMPLATE_NOT_FOUND", details={"rule_key": rule_key}
                )

            # 强制重新生成
            logger.info(f"强制重新生成模板: {rule_key}")
            return await self._generate_template_if_needed(template)

        except Exception as e:
            error_msg = f"重新生成模板失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="TEMPLATE_REGENERATION_FAILED", details={"rule_key": rule_key}
            ) from e

    def get_template_status(self) -> list[dict[str, any]]:
        """
        获取所有模板的状态信息

        Returns:
            List[Dict]: 模板状态列表
        """
        try:
            # 获取所有规则模板
            templates = self.session.query(RuleTemplate).all()

            # 获取文件信息
            file_info_map = {info["rule_key"]: info for info in self.file_manager.get_all_template_files()}

            status_list = []
            for template in templates:
                current_version = self.version_manager.calculate_template_version(template)
                recorded_version = self.version_manager.get_current_version(template.rule_key)
                file_info = file_info_map.get(template.rule_key, {})

                status = {
                    "rule_key": template.rule_key,
                    "rule_name": template.name,
                    "current_version": current_version,
                    "recorded_version": recorded_version,
                    "version_changed": current_version != recorded_version,
                    "file_exists": file_info.get("exists", False),
                    "file_path": file_info.get("file_path"),
                    "file_size": file_info.get("file_size", 0),
                    "last_modified": file_info.get("last_modified"),
                    "field_count": len(template.get_field_metadata_list()),
                }
                status_list.append(status)

            return status_list

        except Exception as e:
            error_msg = f"获取模板状态失败: {str(e)}"
            logger.error(error_msg)
            raise ServiceError(error_msg, error_code="STATUS_QUERY_FAILED") from e

    async def cleanup_all_templates(self):
        """清理所有模板文件"""
        try:
            files = list(self.file_manager.base_dir.glob("*.xlsx"))
            cleaned_count = 0

            for file in files:
                try:
                    file.unlink()
                    cleaned_count += 1
                except Exception as e:
                    logger.warning(f"删除文件失败: {file}, error: {e}")

            # 清理版本记录
            self.version_manager.versions.clear()
            self.version_manager._save_versions()

            logger.info(f"清理完成: 删除 {cleaned_count} 个模板文件")

        except Exception as e:
            error_msg = f"清理模板文件失败: {str(e)}"
            logger.error(error_msg)
            raise ServiceError(error_msg, error_code="CLEANUP_FAILED") from e
