#!/usr/bin/env python3
"""
规则明细标准化API集成测试
测试新的标准化API接口功能
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from models.database import RuleTemplate, RuleTemplateStatusEnum


class TestRuleDetailsStandardAPI:
    """规则明细标准化API集成测试类"""

    @pytest.fixture
    def test_template(self, db_session: Session):
        """创建测试规则模板"""
        template = RuleTemplate(
            rule_key="test_api_standard",
            rule_type="api_test",
            name="API测试标准化规则模板",
            description="用于API测试的标准化规则模板",
            status=RuleTemplateStatusEnum.READY
        )
        db_session.add(template)
        db_session.commit()
        db_session.refresh(template)
        return template

    def test_create_rule_detail_standard_validation(self, client: TestClient, test_template):
        """测试创建规则明细的数据验证"""
        # 测试数据（缺少必填字段）
        incomplete_data = {
            "rule_id": "test_api_001",
            "rule_name": "API测试规则明细",
            "level1": "API测试一级",
            "level2": "API测试二级",
            "level3": "API测试三级"
            # 缺少其他必填字段
        }

        response = client.post(
            f"/api/v1/rules/details/standard/{test_template.rule_key}",
            json=incomplete_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        assert response.status_code == 200
        response_data = response.json()

        # 应该返回验证失败
        if "detail" in response_data:
            detail = response_data["detail"]
            assert not detail.get("success", True)
        else:
            assert not response_data.get("success", True)

    def test_get_rule_details_standard_empty(self, client: TestClient, test_template):
        """测试查询空的规则明细列表"""
        response = client.get(
            f"/api/v1/rules/details/standard/{test_template.rule_key}",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

        pagination_data = response_data.get("data")
        assert pagination_data is not None
        assert pagination_data.get("total") == 0
        assert pagination_data.get("items") == []

    def test_get_rule_details_standard_pagination(self, client: TestClient, test_template):
        """测试分页参数"""
        response = client.get(
            f"/api/v1/rules/details/standard/{test_template.rule_key}",
            params={"page": 1, "page_size": 5},
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

        pagination_data = response_data.get("data")
        assert pagination_data.get("page") == 1
        assert pagination_data.get("page_size") == 5

    def test_get_rule_details_standard_filters(self, client: TestClient, test_template):
        """测试过滤参数"""
        response = client.get(
            f"/api/v1/rules/details/standard/{test_template.rule_key}",
            params={
                "page": 1,
                "page_size": 10,
                "level1": "测试过滤",
                "type": "测试类型",
                "search": "测试搜索"
            },
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

    def test_nonexistent_rule_key(self, client: TestClient):
        """测试不存在的规则键"""
        response = client.get(
            "/api/v1/rules/details/standard/nonexistent_rule",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        assert response.status_code == 200
        response_data = response.json()

        # 应该返回规则不存在的错误
        if "detail" in response_data:
            detail = response_data["detail"]
            assert not detail.get("success", True)
        else:
            assert not response_data.get("success", True)

    def test_api_response_format(self, client: TestClient, test_template):
        """测试API响应格式的一致性"""
        response = client.get(
            f"/api/v1/rules/details/standard/{test_template.rule_key}",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        assert response.status_code == 200
        response_data = response.json()

        # 验证统一响应格式
        assert "success" in response_data
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert "timestamp" in response_data

    def test_standard_field_names_in_response(self, client: TestClient, test_template):
        """测试响应中使用标准字段名"""
        response = client.get(
            f"/api/v1/rules/details/standard/{test_template.rule_key}",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

        # 即使没有数据，也要验证响应结构
        pagination_data = response_data.get("data")
        assert "items" in pagination_data
        assert "total" in pagination_data
        assert "page" in pagination_data
        assert "page_size" in pagination_data
        assert "total_pages" in pagination_data
