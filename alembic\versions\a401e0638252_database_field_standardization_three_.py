"""database_field_standardization_three_table_structure

Revision ID: a401e0638252
Revises: 94713ed230f9
Create Date: 2025-07-24 18:15:54.017205

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a401e0638252"
down_revision: str | None = "94713ed230f9"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """
    数据库字段标准化：创建三表结构
    - rule_template: 规则模板表
    - rule_detail: 规则明细表（使用标准字段名）
    - rule_field_metadata: 字段元数据表
    """

    # 1. 创建 rule_template 表（规则模板表）
    op.create_table(
        "rule_template",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("rule_key", sa.String(length=100), nullable=False, comment="规则模板类型"),
        sa.Column("rule_type", sa.String(length=100), nullable=False, comment="规则类型"),
        sa.Column("name", sa.String(length=500), nullable=False, comment="规则模板名称"),
        sa.Column("description", sa.Text(), nullable=True, comment="规则模板描述"),
        sa.Column("module_path", sa.String(length=500), nullable=True, comment="Python module path"),
        sa.Column("file_hash", sa.String(length=64), nullable=True, comment="SHA-256 hash"),
        sa.Column(
            "status",
            sa.Enum("NEW", "CHANGED", "READY", "DEPRECATED", name="rule_template_status"),
            nullable=False,
            server_default="NEW",
            comment="规则模板状态",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=False, server_default=sa.text("CURRENT_TIMESTAMP")),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(),
            nullable=False,
            server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("rule_key", "rule_type", name="uk_rule_template"),
        comment="规则模板表",
    )
    op.create_index("idx_rule_template_rule_key", "rule_template", ["rule_key"])
    op.create_index("idx_rule_template_status", "rule_template", ["status"])

    # 2. 创建 rule_detail 表（规则明细表，使用标准字段名）
    op.create_table(
        "rule_detail",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("rule_id", sa.String(length=100), nullable=False, comment="规则ID"),
        sa.Column("rule_key", sa.String(length=100), nullable=False, comment="规则模板类型"),
        # 通用字段（使用标准命名）
        sa.Column("rule_name", sa.String(length=500), nullable=False, comment="规则名称"),
        sa.Column("level1", sa.String(length=255), nullable=False, comment="一级错误类型"),
        sa.Column("level2", sa.String(length=255), nullable=False, comment="二级错误类型"),
        sa.Column("level3", sa.String(length=255), nullable=False, comment="三级错误类型"),
        sa.Column("error_reason", sa.Text(), nullable=False, comment="错误原因"),
        sa.Column("degree", sa.String(length=50), nullable=False, comment="错误程度"),
        sa.Column("reference", sa.Text(), nullable=False, comment="质控依据或参考资料"),
        sa.Column("detail_position", sa.String(length=100), nullable=False, comment="具体位置描述"),
        sa.Column("prompted_fields3", sa.String(length=100), nullable=True, comment="提示字段类型"),
        sa.Column("prompted_fields1", sa.String(length=100), nullable=False, comment="提示字段编码"),
        sa.Column("type", sa.String(length=100), nullable=False, comment="规则类别"),
        sa.Column("pos", sa.String(length=100), nullable=False, comment="适用业务"),
        sa.Column("applicableArea", sa.String(length=100), nullable=False, comment="适用地区"),
        sa.Column("default_use", sa.String(length=50), nullable=False, comment="默认选用"),
        sa.Column("remarks", sa.Text(), nullable=True, comment="备注信息"),
        sa.Column("in_illustration", sa.Text(), nullable=True, comment="入参说明"),
        sa.Column("start_date", sa.String(length=20), nullable=False, comment="开始日期"),
        sa.Column("end_date", sa.String(length=20), nullable=False, comment="结束日期"),
        # 固定的高频字段
        sa.Column("yb_code", sa.Text(), nullable=True, comment="药品编码，逗号分隔"),
        sa.Column("diag_whole_code", sa.Text(), nullable=True, comment="完整诊断编码，逗号分隔"),
        sa.Column("diag_code_prefix", sa.Text(), nullable=True, comment="诊断编码前缀，逗号分隔"),
        sa.Column("diag_name_keyword", sa.String(length=200), nullable=True, comment="诊断名称关键字，逗号分隔"),
        sa.Column("fee_whole_code", sa.Text(), nullable=True, comment="药品/诊疗项目完整编码，逗号分隔"),
        sa.Column("fee_code_prefix", sa.Text(), nullable=True, comment="药品/诊疗项目编码前缀，逗号分隔"),
        # 扩展字段
        sa.Column("extended_fields", sa.Text(), nullable=True, comment="JSON格式的扩展字段"),
        # 元数据
        sa.Column(
            "status",
            sa.Enum("ACTIVE", "INACTIVE", "DEPRECATED", name="rule_detail_status"),
            nullable=False,
            server_default="ACTIVE",
            comment="记录状态",
        ),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=False, server_default=sa.text("CURRENT_TIMESTAMP")),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(),
            nullable=False,
            server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="规则明细表",
    )
    op.create_index("idx_rule_detail_rule_key", "rule_detail", ["rule_key"])
    op.create_index("idx_rule_detail_rule_id", "rule_detail", ["rule_id"])
    op.create_index("idx_rule_detail_status", "rule_detail", ["status"])
    op.create_index("idx_rule_detail_yb_code", "rule_detail", ["yb_code"], mysql_length=100)

    # 3. 创建 rule_field_metadata 表（字段元数据表）
    op.create_table(
        "rule_field_metadata",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("rule_key", sa.String(length=50), nullable=False, comment="规则模板类型"),
        sa.Column("field_name", sa.String(length=100), nullable=False, comment="字段名称"),
        sa.Column(
            "field_type",
            sa.Enum("string", "integer", "array", "boolean", name="field_type_enum"),
            nullable=False,
            comment="字段类型",
        ),
        sa.Column("is_required", sa.Boolean(), nullable=False, server_default="0", comment="是否必填"),
        sa.Column("is_fixed_field", sa.Boolean(), nullable=False, server_default="0", comment="是否为固定字段"),
        sa.Column("display_name", sa.String(length=200), nullable=False, comment="显示名称"),
        sa.Column("description", sa.Text(), nullable=True, comment="字段描述"),
        sa.Column("validation_rule", sa.Text(), nullable=True, comment="JSON格式的校验规则"),
        sa.Column("default_value", sa.Text(), nullable=True, comment="默认值"),
        sa.Column("excel_column_order", sa.Integer(), nullable=True, comment="Excel列顺序"),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=False, server_default=sa.text("CURRENT_TIMESTAMP")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("rule_key", "field_name", name="uk_rule_field"),
        comment="字段元数据表",
    )
    op.create_index("idx_rule_field_metadata_rule_key", "rule_field_metadata", ["rule_key"])


def downgrade() -> None:
    """
    回滚操作：删除三表结构
    """
    # 删除表（按依赖关系逆序）
    op.drop_table("rule_field_metadata")
    op.drop_table("rule_detail")
    op.drop_table("rule_template")

    # 删除枚举类型
    op.execute("DROP TYPE IF EXISTS rule_template_status")
    op.execute("DROP TYPE IF EXISTS rule_detail_status")
    op.execute("DROP TYPE IF EXISTS field_type_enum")
