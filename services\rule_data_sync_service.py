"""
规则数据同步服务 - 重构版本
适配新的三表结构（RuleDetail、RuleTemplate、RuleFieldMetadata）
支持增量同步、压缩传输和错误恢复
"""

import gzip
import json
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Any

from sqlalchemy.orm import Session, selectinload

from core.logging.logging_system import log as logger
from core.rule_cache import RULE_CACHE
from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate, RuleTemplateStatusEnum
from rules.rule_registry import RuleRegistry
from services.rule_detail_service import ServiceError
from services.unified_data_mapping_engine import UnifiedDataMappingEngine


@dataclass
class SyncStatistics:
    """同步统计信息"""

    total_templates: int
    total_details: int
    total_metadata: int
    sync_duration: float
    cache_size_mb: float
    compression_ratio: float
    last_sync_time: float


class RuleDataSyncService:
    """规则数据同步服务 - 重构版本"""

    def __init__(self, session: Session = None, cache_file_path: str = "rules_cache.json.gz"):
        """
        初始化服务

        Args:
            session: 数据库会话（主节点使用）
            cache_file_path: 缓存文件路径
        """
        self.session = session
        self.cache_file_path = Path(cache_file_path)
        self.data_mapping_engine = UnifiedDataMappingEngine()
        self.rule_registry = RuleRegistry()

        # 同步配置
        self.compression_level = 6
        self.batch_size = 1000

    def sync_from_database(self, force_full_sync: bool = False) -> SyncStatistics:
        """
        从数据库同步规则数据到本地缓存

        Args:
            force_full_sync: 是否强制全量同步

        Returns:
            SyncStatistics: 同步统计信息

        Raises:
            ServiceException: 当同步失败时
        """
        if not self.session:
            raise ServiceError("数据库会话未初始化，无法从数据库同步", error_code="SESSION_NOT_INITIALIZED")

        start_time = time.time()

        try:
            logger.info(f"开始从数据库同步规则数据: force_full={force_full_sync}")

            # 1. 获取同步数据
            sync_data = self._load_data_from_database(force_full_sync)

            # 2. 转换数据格式
            cache_data = self._convert_to_cache_format(sync_data)

            # 3. 保存到缓存文件
            original_size, compressed_size = self._save_cache_file(cache_data)

            # 4. 更新内存缓存
            self._update_memory_cache(cache_data)

            # 5. 构建统计信息
            duration = time.time() - start_time
            stats = SyncStatistics(
                total_templates=len(sync_data["templates"]),
                total_details=len(sync_data["details"]),
                total_metadata=sum(len(t.get("metadata", [])) for t in sync_data["templates"]),
                sync_duration=duration,
                cache_size_mb=compressed_size / (1024 * 1024),
                compression_ratio=original_size / compressed_size if compressed_size > 0 else 1.0,
                last_sync_time=time.time(),
            )

            logger.info(
                f"数据库同步完成: templates={stats.total_templates}, "
                f"details={stats.total_details}, metadata={stats.total_metadata}, "
                f"duration={duration:.2f}s, size={stats.cache_size_mb:.2f}MB, "
                f"compression={stats.compression_ratio:.2f}x"
            )

            return stats

        except Exception as e:
            error_msg = f"从数据库同步失败: error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(error_msg, error_code="DATABASE_SYNC_FAILED", details={"error": str(e)}) from None

    def load_from_cache(self) -> SyncStatistics:
        """
        从本地缓存加载规则数据

        Returns:
            SyncStatistics: 加载统计信息

        Raises:
            ServiceException: 当加载失败时
        """
        start_time = time.time()

        try:
            if not self.cache_file_path.exists():
                raise ServiceError(
                    f"缓存文件不存在: {self.cache_file_path}",
                    error_code="CACHE_FILE_NOT_FOUND",
                    details={"cache_path": str(self.cache_file_path)},
                )

            logger.info(f"开始从缓存加载规则数据: {self.cache_file_path}")

            # 1. 读取缓存文件
            cache_data = self._load_cache_file()

            # 2. 更新内存缓存
            self._update_memory_cache(cache_data)

            # 3. 构建统计信息
            duration = time.time() - start_time
            file_size = self.cache_file_path.stat().st_size

            stats = SyncStatistics(
                total_templates=len(cache_data.get("templates", [])),
                total_details=len(cache_data.get("details", [])),
                total_metadata=sum(len(t.get("metadata", [])) for t in cache_data.get("templates", [])),
                sync_duration=duration,
                cache_size_mb=file_size / (1024 * 1024),
                compression_ratio=1.0,  # 从缓存加载时无法计算压缩比
                last_sync_time=cache_data.get("sync_time", time.time()),
            )

            logger.info(
                f"缓存加载完成: templates={stats.total_templates}, "
                f"details={stats.total_details}, metadata={stats.total_metadata}, "
                f"duration={duration:.2f}s, size={stats.cache_size_mb:.2f}MB"
            )

            return stats

        except Exception as e:
            error_msg = f"从缓存加载失败: error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg,
                error_code="CACHE_LOAD_FAILED",
                details={"error": str(e), "cache_path": str(self.cache_file_path)},
            ) from None

    def get_sync_status(self) -> dict[str, Any]:
        """
        获取同步状态信息

        Returns:
            Dict: 同步状态信息
        """
        try:
            status = {
                "cache_file_exists": self.cache_file_path.exists(),
                "cache_file_path": str(self.cache_file_path),
                "memory_cache_size": len(RULE_CACHE),
                "database_available": self.session is not None,
            }

            if self.cache_file_path.exists():
                file_stat = self.cache_file_path.stat()
                status.update(
                    {"cache_file_size_mb": file_stat.st_size / (1024 * 1024), "cache_file_modified": file_stat.st_mtime}
                )

            if self.session:
                try:
                    template_count = self.session.query(RuleTemplate).count()
                    detail_count = self.session.query(RuleDetail).count()
                    status.update({"database_templates": template_count, "database_details": detail_count})
                except Exception as e:
                    status["database_error"] = str(e)

            return status

        except Exception as e:
            logger.error(f"获取同步状态失败: error={e}")
            return {"error": str(e)}

    def _load_data_from_database(self, force_full_sync: bool) -> dict[str, Any]:
        """从数据库加载数据"""
        # 1. 加载规则模板和字段元数据
        templates_query = (
            self.session.query(RuleTemplate)
            .options(selectinload(RuleTemplate.field_metadata))
            .filter(RuleTemplate.status == RuleTemplateStatusEnum.ACTIVE)
        )

        templates = templates_query.all()

        # 2. 加载规则明细
        details_query = self.session.query(RuleDetail).filter(RuleDetail.status == RuleDetailStatusEnum.ACTIVE)

        details = details_query.all()

        # 3. 构建同步数据
        sync_data = {
            "templates": [self._template_to_dict(template) for template in templates],
            "details": [self._detail_to_dict(detail) for detail in details],
            "sync_time": time.time(),
            "sync_type": "full" if force_full_sync else "incremental",
        }

        logger.debug(f"从数据库加载数据: templates={len(templates)}, details={len(details)}")

        return sync_data

    def _template_to_dict(self, template: RuleTemplate) -> dict[str, Any]:
        """将RuleTemplate转换为字典"""
        return {
            "rule_key": template.rule_key,
            "name": template.name,
            "description": template.description,
            "status": template.status.value,
            "metadata": [
                {
                    "field_name": metadata.field_name,
                    "display_name": metadata.display_name,
                    "field_type": metadata.field_type,
                    "is_required": metadata.is_required,
                    "validation_rules": metadata.validation_rules,
                    "default_value": metadata.default_value,
                    "description": metadata.description,
                    "excel_column_order": metadata.excel_column_order,
                }
                for metadata in template.field_metadata
            ],
            "created_at": template.created_at.timestamp() if template.created_at else None,
            "updated_at": template.updated_at.timestamp() if template.updated_at else None,
        }

    def _detail_to_dict(self, detail: RuleDetail) -> dict[str, Any]:
        """将RuleDetail转换为字典"""
        detail_dict = detail.to_dict()

        # 转换时间戳
        if detail.created_at:
            detail_dict["created_at"] = detail.created_at.timestamp()
        if detail.updated_at:
            detail_dict["updated_at"] = detail.updated_at.timestamp()

        return detail_dict

    def _convert_to_cache_format(self, sync_data: dict[str, Any]) -> dict[str, Any]:
        """转换为缓存格式"""
        cache_data = {
            "version": "2.0",  # 新版本格式
            "sync_time": sync_data["sync_time"],
            "sync_type": sync_data["sync_type"],
            "templates": sync_data["templates"],
            "details": [],
            "rule_instances": {},
        }

        # 转换规则明细为规则实例格式
        for detail in sync_data["details"]:
            try:
                # 使用数据映射引擎转换格式
                legacy_format = self.data_mapping_engine.convert_to_legacy_format(detail)
                rule_key = detail.get("rule_key")

                if rule_key and legacy_format:
                    if rule_key not in cache_data["rule_instances"]:
                        cache_data["rule_instances"][rule_key] = []
                    cache_data["rule_instances"][rule_key].append(legacy_format)

                cache_data["details"].append(detail)

            except Exception as e:
                logger.warning(f"转换规则明细失败: detail_id={detail.get('rule_detail_id')}, error={e}")

        return cache_data

    def _save_cache_file(self, cache_data: dict[str, Any]) -> tuple[int, int]:
        """保存缓存文件"""
        # 序列化数据
        json_data = json.dumps(cache_data, ensure_ascii=False, separators=(",", ":"))
        original_size = len(json_data.encode("utf-8"))

        # 压缩保存
        with gzip.open(self.cache_file_path, "wt", encoding="utf-8", compresslevel=self.compression_level) as f:
            f.write(json_data)

        compressed_size = self.cache_file_path.stat().st_size

        logger.debug(
            f"保存缓存文件: original={original_size}, compressed={compressed_size}, "
            f"ratio={original_size/compressed_size:.2f}x"
        )

        return original_size, compressed_size

    def _load_cache_file(self) -> dict[str, Any]:
        """加载缓存文件"""
        with gzip.open(self.cache_file_path, "rt", encoding="utf-8") as f:
            cache_data = json.load(f)

        logger.debug(f"加载缓存文件: version={cache_data.get('version', 'unknown')}")
        return cache_data

    def _update_memory_cache(self, cache_data: dict[str, Any]):
        """更新内存缓存"""
        try:
            # 清空现有缓存
            RULE_CACHE.clear()

            # 加载规则实例
            rule_instances = cache_data.get("rule_instances", {})

            for rule_key, instances in rule_instances.items():
                try:
                    # 使用规则注册表创建规则实例
                    rule_class = self.rule_registry.get_rule_class(rule_key)
                    if rule_class:
                        for instance_data in instances:
                            rule_instance = rule_class(**instance_data)
                            RULE_CACHE[rule_instance.rule_id] = rule_instance
                    else:
                        logger.warning(f"未找到规则类: rule_key={rule_key}")

                except Exception as e:
                    logger.error(f"创建规则实例失败: rule_key={rule_key}, error={e}")

            logger.info(f"内存缓存更新完成: loaded_rules={len(RULE_CACHE)}")

        except Exception as e:
            logger.error(f"更新内存缓存失败: error={e}")
            raise
