"""
Excel模板预生成集成测试
"""

import tempfile
from pathlib import Path

import pytest

from models.database import Base, FieldTypeEnum, RuleFieldMetadata, RuleTemplate
from services.template_pre_generation_service import TemplatePreGenerationService


@pytest.fixture
def test_db():
    """创建测试数据库"""
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy.pool import StaticPool

    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )

    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    yield SessionLocal

    Base.metadata.drop_all(engine)
    engine.dispose()


@pytest.fixture
def sample_rule_template(test_db):
    """创建示例规则模板"""
    session = test_db()
    try:
        # 创建规则模板
        from models.database import RuleTemplateStatusEnum

        template = RuleTemplate(
            rule_key="test_drug_limit",
            rule_type="药品限制",
            name="测试药品限制规则",
            description="用于测试的药品限制规则模板",
            module_path="rules.drug_limit",
            file_hash="test_hash_123",
            status=RuleTemplateStatusEnum.READY,
        )
        session.add(template)
        session.commit()
        session.refresh(template)

        # 创建字段元数据
        fields = [
            {
                "field_name": "rule_name",
                "field_type": FieldTypeEnum.STRING,
                "is_required": True,
                "display_name": "规则名称",
                "excel_column_order": 1,
                "validation_rule": "required|max_length:500",
            },
            {
                "field_name": "level1",
                "field_type": FieldTypeEnum.STRING,
                "is_required": True,
                "display_name": "一级错误类型",
                "excel_column_order": 2,
                "validation_rule": "required|max_length:255",
            },
            {
                "field_name": "yb_code",
                "field_type": FieldTypeEnum.STRING,
                "is_required": False,
                "display_name": "药品编码",
                "excel_column_order": 3,
                "validation_rule": "max_length:1000",
            },
            {
                "field_name": "age_threshold",
                "field_type": FieldTypeEnum.INTEGER,
                "is_required": False,
                "display_name": "年龄阈值",
                "excel_column_order": 4,
                "validation_rule": "integer|min:0|max:150",
            },
        ]

        for field_data in fields:
            field_metadata = RuleFieldMetadata(rule_key=template.rule_key, **field_data)
            session.add(field_metadata)

        session.commit()
        return template

    finally:
        session.close()


class TestExcelTemplatePreGeneration:
    """Excel模板预生成集成测试"""

    @pytest.mark.asyncio
    async def test_complete_template_generation_workflow(self, test_db, sample_rule_template):
        """测试完整的模板生成工作流程"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session = test_db()
            try:
                # 初始化服务
                service = TemplatePreGenerationService(session, temp_dir)

                # 模拟Excel服务生成（因为我们没有完整的Excel依赖）
                async def mock_generate_excel(rule_key):
                    # 创建一个模拟的Excel文件
                    mock_file = Path(temp_dir) / f"{rule_key}_temp.xlsx"
                    mock_file.write_text("mock excel content")
                    return mock_file

                # 替换Excel服务的生成方法
                original_method = service.excel_service.generate_template_by_rule_key
                service.excel_service.generate_template_by_rule_key = mock_generate_excel

                # 执行模板生成
                result = await service.generate_all_templates_on_startup()

                # 验证结果
                assert result["total"] == 1
                assert result["generated"] == 1
                assert result["skipped"] == 0
                assert result["failed"] == 0
                assert result["duration"] > 0

                # 验证文件是否生成
                template_files = list(Path(temp_dir).glob("test_drug_limit_*.xlsx"))
                assert len(template_files) == 1

                # 验证版本记录
                version = service.version_manager.get_current_version("test_drug_limit")
                assert version is not None
                assert len(version) == 8

                # 验证文件管理器可以找到文件
                found_file = service.file_manager.get_template_file("test_drug_limit")
                assert found_file is not None
                assert found_file.exists()

            finally:
                session.close()

    @pytest.mark.asyncio
    async def test_template_status_reporting(self, test_db, sample_rule_template):
        """测试模板状态报告"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session = test_db()
            try:
                service = TemplatePreGenerationService(session, temp_dir)

                # 获取状态（生成前）
                status_before = service.get_template_status()
                assert len(status_before) == 1
                assert status_before[0]["rule_key"] == "test_drug_limit"
                assert status_before[0]["file_exists"] is False

                # 模拟生成文件
                version = service.version_manager.calculate_template_version(sample_rule_template)
                mock_file = service.file_manager.get_standard_path("test_drug_limit", version)
                mock_file.touch()

                # 更新版本记录
                service.version_manager.update_version_record("test_drug_limit", version)

                # 获取状态（生成后）
                status_after = service.get_template_status()
                assert len(status_after) == 1
                assert status_after[0]["rule_key"] == "test_drug_limit"
                assert status_after[0]["file_exists"] is True
                assert status_after[0]["current_version"] == version
                assert status_after[0]["recorded_version"] == version
                assert status_after[0]["version_changed"] is False

            finally:
                session.close()

    @pytest.mark.asyncio
    async def test_version_change_detection(self, test_db, sample_rule_template):
        """测试版本变更检测"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session = test_db()
            try:
                service = TemplatePreGenerationService(session, temp_dir)

                # 计算初始版本
                initial_version = service.version_manager.calculate_template_version(sample_rule_template)
                service.version_manager.update_version_record("test_drug_limit", initial_version)

                # 修改模板（添加新字段）
                new_field = RuleFieldMetadata(
                    rule_key="test_drug_limit",
                    field_name="new_field",
                    field_type=FieldTypeEnum.STRING,
                    is_required=False,
                    display_name="新字段",
                    excel_column_order=5,
                )
                session.add(new_field)
                session.commit()

                # 重新获取模板（模拟字段元数据变更）
                updated_template = (
                    session.query(RuleTemplate).filter(RuleTemplate.rule_key == "test_drug_limit").first()
                )

                # 计算新版本
                new_version = service.version_manager.calculate_template_version(updated_template)

                # 验证版本已变更
                assert new_version != initial_version

                # 验证版本变更检测
                assert service.version_manager.is_version_changed(updated_template) is True

            finally:
                session.close()

    @pytest.mark.asyncio
    async def test_regenerate_single_template(self, test_db, sample_rule_template):
        """测试重新生成单个模板"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session = test_db()
            try:
                service = TemplatePreGenerationService(session, temp_dir)

                # 模拟Excel生成
                async def mock_generate_excel(rule_key):
                    mock_file = Path(temp_dir) / f"{rule_key}_temp.xlsx"
                    mock_file.write_text("mock excel content")
                    return mock_file

                service.excel_service.generate_template_by_rule_key = mock_generate_excel

                # 重新生成指定模板
                result = await service.regenerate_template("test_drug_limit")

                # 验证结果
                assert result is True

                # 验证文件生成
                template_files = list(Path(temp_dir).glob("test_drug_limit_*.xlsx"))
                assert len(template_files) == 1

            finally:
                session.close()

    @pytest.mark.asyncio
    async def test_cleanup_all_templates(self, test_db, sample_rule_template):
        """测试清理所有模板"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session = test_db()
            try:
                service = TemplatePreGenerationService(session, temp_dir)

                # 创建一些模拟文件
                test_files = [
                    Path(temp_dir) / "test_rule1_abc123.xlsx",
                    Path(temp_dir) / "test_rule2_def456.xlsx",
                    Path(temp_dir) / "other_file.txt",
                ]

                for file in test_files:
                    file.touch()

                # 添加版本记录
                service.version_manager.update_version_record("test_rule1", "abc123")
                service.version_manager.update_version_record("test_rule2", "def456")

                # 执行清理
                await service.cleanup_all_templates()

                # 验证Excel文件被删除
                assert not test_files[0].exists()
                assert not test_files[1].exists()
                assert test_files[2].exists()  # 非Excel文件应该保留

                # 验证版本记录被清理
                assert service.version_manager.get_current_version("test_rule1") is None
                assert service.version_manager.get_current_version("test_rule2") is None

            finally:
                session.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
