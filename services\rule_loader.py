import gzip
import inspect
import json
import os
import traceback
from collections import defaultdict
from typing import TypeV<PERSON>

from sqlalchemy.orm import Session

from core.db_session import get_session_factory
from core.logging.logging_system import log as logger
from core.rule_cache import RULE_CACHE
from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate, RuleTemplateStatusEnum
from rules.rule_registry import RuleRegistry
from services.data_mapping_engine import DataMappingEngine

# --- Constants ---
LOCAL_RULES_PATH = "rules_cache.json.gz"
T = TypeVar("T")


def _convert_rule_detail_to_cache_format(rule_detail: RuleDetail) -> dict:
    """
    将 RuleDetail 对象转换为规则实例化所需的格式

    Args:
        rule_detail: RuleDetail 对象

    Returns:
        dict: 规则实例化格式的数据
    """
    try:
        # 使用已实现的 data_mapping_engine 反向映射
        mapping_engine = DataMappingEngine()

        # 将 RuleDetail 转换为字典
        detail_dict = rule_detail.to_dict()

        # 使用反向映射转换为传统格式
        converted = mapping_engine.convert_structured_to_legacy([detail_dict])

        if converted and len(converted) > 0:
            result = converted[0]
            # 确保 rule_id 字段存在
            if "rule_id" not in result and rule_detail.rule_detail_id:
                result["rule_id"] = rule_detail.rule_detail_id
            return result
        else:
            # 降级处理：直接使用基本字段映射
            logger.warning(
                f"Failed to convert rule detail {rule_detail.rule_detail_id} using mapping engine, using fallback"
            )
            return _fallback_rule_detail_conversion(rule_detail)

    except Exception as e:
        logger.error(f"Error converting rule detail {rule_detail.rule_detail_id}: {e}")
        # 降级处理
        return _fallback_rule_detail_conversion(rule_detail)


def _fallback_rule_detail_conversion(rule_detail: RuleDetail) -> dict:
    """
    降级的 RuleDetail 转换方法，直接映射基本字段

    Args:
        rule_detail: RuleDetail 对象

    Returns:
        dict: 基本的规则实例化格式数据
    """
    # 基本字段映射
    result = {
        "rule_id": rule_detail.rule_detail_id,
        "rule_name": rule_detail.rule_name or "",
        "level1": rule_detail.error_level_1 or "",
        "level2": rule_detail.error_level_2 or "",
        "level3": rule_detail.error_level_3 or "",
        "error_reason": rule_detail.error_reason or "",
        "degree": rule_detail.error_severity or "",
        "reference": rule_detail.quality_basis or "",
        "detail_position": rule_detail.location_desc or "",
        "prompted_fields3": rule_detail.prompt_field_type or "",
        "prompted_fields1": rule_detail.prompt_field_code or "",
        "prompted_fields2": str(rule_detail.prompt_field_seq or ""),
        "type": rule_detail.rule_category or "",
        "pos": rule_detail.applicable_business or "",
        "applicableArea": rule_detail.applicable_region or "",
        "default_use": "是" if rule_detail.default_selected else "否",
        "error_fee": float(rule_detail.involved_amount or 0),
        "remarks": rule_detail.remark or "",
        "used_count": float(rule_detail.usage_quantity or 0),
        "illegal_count": float(rule_detail.violation_quantity or 0),
        "used_day": rule_detail.usage_days or 0,
        "illegal_day": rule_detail.violation_days or 0,
        "illegal_item": rule_detail.violation_items or "",
    }

    # 从 extra_data 中恢复规则特定字段（如 yb_code）
    if rule_detail.extra_data and isinstance(rule_detail.extra_data, dict):
        for key, value in rule_detail.extra_data.items():
            if key not in result and value is not None:
                result[key] = value
                logger.debug(f"从 extra_data 恢复字段: {key} = {value}")

    return result


def _load_rule_details_as_dict(session: Session, rule_key: str) -> list[dict]:
    """
    从 RuleDetail 表按 rule_key 加载数据并转换为规则实例化格式（适配新模型）

    Args:
        session: 数据库会话
        rule_key: 规则模板键

    Returns:
        list[dict]: 转换后的规则数据
    """
    import time

    start_time = time.time()

    try:
        # 查询指定 rule_key 的所有活跃 RuleDetail 数据
        rule_details = (
            session.query(RuleDetail)
            .filter(
                RuleDetail.rule_key == rule_key,
                RuleDetail.status == RuleDetailStatusEnum.ACTIVE
            )
            .all()
        )

        if not rule_details:
            logger.warning(f"No active rule details found for rule_key {rule_key}")
            return []

        # 性能优化：批量转换，减少函数调用开销
        converted_data = []
        conversion_errors = 0

        for detail in rule_details:
            try:
                converted_item = _convert_rule_detail_to_cache_format(detail)
                if converted_item:  # 只添加成功转换的数据
                    converted_data.append(converted_item)
            except Exception as e:
                conversion_errors += 1
                logger.warning(f"Failed to convert rule detail {detail.rule_detail_id}: {e}")
                continue

        # 性能监控
        load_time = time.time() - start_time
        logger.info(
            f"Loaded {len(converted_data)} rule details from database for rule_key {rule_key} "
            f"in {load_time:.3f}s (conversion errors: {conversion_errors})"
        )

        # 性能统计
        if load_time > 1.0:  # 如果加载时间超过1秒，记录警告
            logger.warning(f"Slow rule detail loading detected: {load_time:.3f}s for rule_key {rule_key}")

        return converted_data

    except Exception as e:
        load_time = time.time() - start_time
        logger.error(f"Failed to load rule details for rule_key {rule_key} after {load_time:.3f}s: {e}")
        return []


# 新模型不再需要这些基于 RuleDataSet 的函数，直接使用 RuleDetail 和 RuleTemplate


def _detect_enhanced_cache_format(rule_datasets_data: list) -> bool:
    """
    检测缓存文件是否为增强格式（包含 RuleDetail 数据）

    Args:
        rule_datasets_data: 规则数据集列表

    Returns:
        bool: 是否为增强格式
    """
    if not rule_datasets_data or not isinstance(rule_datasets_data, list):
        return False

    # 检查第一个数据集是否包含增强格式的标识字段
    first_dataset = rule_datasets_data[0]
    if not isinstance(first_dataset, dict):
        return False

    # 增强格式的标识：包含 data_source 字段或 rule_details 字段
    enhanced_indicators = ["data_source", "rule_details", "template_info"]

    for indicator in enhanced_indicators:
        if indicator in first_dataset:
            logger.debug(f"Enhanced format detected by indicator: {indicator}")
            return True

    return False


def _load_from_enhanced_format(rule_datasets_data: list, all_rule_classes: dict) -> bool:
    """
    从增强格式的缓存文件加载规则（支持 RuleDetail 数据）

    Args:
        rule_datasets_data: 增强格式的规则数据集列表
        all_rule_classes: 所有规则类的映射

    Returns:
        bool: 加载是否成功
    """
    try:
        logger.info(f"Loading {len(rule_datasets_data)} rule datasets from enhanced format")

        # 重建兼容对象列表
        reconstructed_datasets = []

        for dataset_data in rule_datasets_data:
            try:
                # 重建兼容数据对象
                dataset = _reconstruct_dataset_from_cache(dataset_data)
                if dataset:
                    reconstructed_datasets.append(dataset)
                else:
                    logger.warning(
                        f"Failed to reconstruct dataset from cache data: {dataset_data.get('id', 'unknown')}"
                    )

            except Exception as e:
                logger.error(f"Error reconstructing dataset: {e}")
                continue

        if not reconstructed_datasets:
            logger.error("No valid datasets could be reconstructed from enhanced format")
            return False

        logger.info(f"Successfully reconstructed {len(reconstructed_datasets)} datasets")

        # 使用现有的缓存填充逻辑
        _populate_cache_from_data(reconstructed_datasets, all_rule_classes)

        return True

    except Exception as e:
        logger.error(f"Failed to load from enhanced format: {e}")
        return False


def _reconstruct_dataset_from_cache(dataset_data: dict):
    """
    从缓存数据重建兼容的数据集对象

    Args:
        dataset_data: 缓存中的数据集数据

    Returns:
        兼容对象: 重建的数据集对象，失败时返回 None
    """
    try:
        # 创建简单的数据对象来模拟旧的接口
        class CacheDataset:
            def __init__(self):
                self.id = None
                self.is_active = True
                self.template_info = None
                self.data_set = []

        class CacheTemplate:
            def __init__(self):
                self.id = None
                self.rule_key = None
                self.name = None
                self.status = RuleTemplateStatusEnum.ACTIVE

        # 创建数据集对象
        dataset = CacheDataset()

        # 基本字段
        dataset.id = dataset_data.get("id")
        dataset.is_active = dataset_data.get("is_active", True)

        # 重建模板信息
        template_data = dataset_data.get("template_info", {})
        template = CacheTemplate()
        template.id = template_data.get("id")
        template.rule_key = template_data.get("rule_key")
        template.name = template_data.get("name")
        template.status = RuleTemplateStatusEnum.ACTIVE  # 缓存中的都是活跃状态
        dataset.template_info = template

        # 数据源选择
        data_source = dataset_data.get("data_source", "json")

        if data_source == "rule_details" and "rule_details" in dataset_data:
            # 使用 RuleDetail 数据
            dataset.data_set = dataset_data["rule_details"]
            logger.debug(f"Using rule_details data for dataset {dataset.id}")
        else:
            # 使用传统 JSON 数据
            dataset.data_set = dataset_data.get("data_set", [])
            logger.debug(f"Using traditional JSON data for dataset {dataset.id}")

        return dataset

    except Exception as e:
        logger.error(f"Failed to reconstruct dataset from cache: {e}")
        return None


def _optimize_memory_usage():
    """内存使用优化策略（增强版）"""
    import gc
    import time

    start_time = time.time()

    # 强制垃圾回收
    collected = gc.collect()

    # 记录内存使用情况
    try:
        import psutil

        process = psutil.Process()
        memory_info = process.memory_info()

        # 计算内存使用率
        memory_mb = memory_info.rss / 1024 / 1024
        memory_percent = process.memory_percent()

        logger.info(
            f"Memory optimization completed in {time.time() - start_time:.3f}s: "
            f"{memory_mb:.2f} MB ({memory_percent:.1f}%), collected {collected} objects"
        )

        # 内存使用警告
        if memory_mb > 500:  # 超过500MB发出警告
            logger.warning(f"High memory usage detected: {memory_mb:.2f} MB")

    except ImportError:
        logger.debug(f"Memory optimization completed in {time.time() - start_time:.3f}s, collected {collected} objects")


def load_rules_from_db(session: Session) -> dict[str, list[dict]]:
    """
    从数据库加载活跃的规则模板和明细数据
    
    Returns:
        dict: rule_key 到规则数据列表的映射
    """
    logger.info("Loading active rule templates and details from database...")

    # 查询所有活跃的规则模板
    active_templates = (
        session.query(RuleTemplate)
        .filter(RuleTemplate.status == RuleTemplateStatusEnum.ACTIVE)
        .all()
    )

    logger.info(f"Found {len(active_templates)} active rule templates")

    # 按规则键组织数据
    rules_by_key = {}

    for template in active_templates:
        rule_key = template.rule_key
        logger.debug(f"Loading rule details for template: {template.name} (key: {rule_key})")

        # 加载该模板下的所有规则明细
        rule_data = _load_rule_details_as_dict(session, rule_key)

        if rule_data:
            rules_by_key[rule_key] = rule_data
            logger.debug(f"Loaded {len(rule_data)} rule details for key {rule_key}")
        else:
            logger.warning(f"No rule details found for template {rule_key}")

    logger.info(f"Successfully loaded rules for {len(rules_by_key)} rule keys")
    return rules_by_key


def _populate_cache_from_data(rules_data, rule_classes: dict[str, type[T]]):
    """
    从规则数据填充缓存
    
    Args:
        rules_data: 规则数据，可以是兼容对象列表或规则键到数据的映射
        rule_classes: 规则类映射
    """
    logger.info("Starting to populate cache from rule data.")

    total_rules_loaded = 0
    new_cache: dict[str, T] = {}

    # 处理不同类型的输入
    data_sets_by_rule_key: dict[str, list[dict]] = defaultdict(list)

    if isinstance(rules_data, dict):
        # 直接是 rule_key -> data 的映射
        data_sets_by_rule_key = rules_data
        logger.info(f"Processing {len(rules_data)} rule keys from direct mapping")
    elif isinstance(rules_data, list):
        # 兼容对象列表
        logger.info(f"Processing {len(rules_data)} compatibility objects")
        for dataset in rules_data:
            if hasattr(dataset, 'template_info') and dataset.template_info:
                rule_key = dataset.template_info.rule_key
                rule_data = dataset.data_set or []
                if rule_data:
                    data_sets_by_rule_key[rule_key].extend(rule_data)
    else:
        logger.error(f"Unsupported rules_data type: {type(rules_data)}")
        return

    # 处理规则实例化逻辑
    for rule_key, data_items in data_sets_by_rule_key.items():
        rule_cls = rule_classes.get(rule_key)
        if not rule_cls:
            logger.warning(f"Rule class for key '{rule_key}' not found in registry. Skipping.")
            continue

        try:
            sig = inspect.signature(rule_cls.__init__)
            expected_params = {
                p.name
                for p in sig.parameters.values()
                if p.kind
                in (
                    inspect.Parameter.POSITIONAL_OR_KEYWORD,
                    inspect.Parameter.KEYWORD_ONLY,
                )
                and p.name != "self"
                and p.name != "rule_id"
            }
        except (ValueError, TypeError):
            logger.warning(
                f"Could not determine constructor signature for {rule_key}. Skipping parameter validation."
            )
            expected_params = None

        for item in data_items:
            # Step 1: Extract rule_id and keep it separate.
            rule_id = item.pop("rule_id", None)
            if not rule_id:
                logger.warning(
                    f"Skipping data item for rule '{rule_key}' because it lacks a 'rule_id'. Data: {item}"
                )
                continue
            final_kwargs = item.copy()

            if expected_params:
                # 这里不应该包含 rule_id，在最后的生成中，会单独指定
                if "rule_id" in expected_params:
                    expected_params.remove("rule_id")

                for param in expected_params:
                    if param not in final_kwargs:
                        final_kwargs[param] = ""

                extra_keys = set(final_kwargs.keys()) - expected_params
                if extra_keys:
                    logger.debug(f"Removing extra keys {extra_keys} from data for rule {rule_key}")
                    for key in extra_keys:
                        del final_kwargs[key]

            try:
                # Step 2: Pass rule_id explicitly during instantiation.
                instance = rule_cls(rule_id=rule_id, **final_kwargs)

                # Step 3: Use rule_id as the key in the new cache.
                new_cache[rule_id] = instance
                total_rules_loaded += 1
            except TypeError as e:
                logger.error(
                    f"Failed to instantiate rule for id {rule_id}. "
                    f"Mismatched parameters: {e}. Provided args: {final_kwargs}",
                )
                logger.error(traceback.format_exc())
            except Exception as e:
                logger.error(
                    f"An unexpected error occurred while instantiating rule for id {rule_id}: {e}",
                    exc_info=True,
                )

    RULE_CACHE.clear()
    RULE_CACHE.update(new_cache)
    logger.info(f"Finished populating cache. Total concrete rule instances loaded: {total_rules_loaded}.")

    # 内存使用优化
    _optimize_memory_usage()


def load_rules_into_cache(session: Session | None = None):
    """
    Main entry point for loading rules into the cache from the database.
    Can use an existing session or create a new one.
    """
    logger.info("Starting to load rules into memory cache...")
    rule_registry = RuleRegistry()
    rule_registry.auto_discover_rules()
    all_rule_classes = rule_registry.get_all_rules()

    def _load(sess: Session):
        rules_by_key = load_rules_from_db(sess)
        if not rules_by_key:
            logger.warning("No rules found in the database to load into cache.")
            RULE_CACHE.clear()  # Ensure cache is empty if no rules are found
            return
        _populate_cache_from_data(rules_by_key, all_rule_classes)

    if session:
        _load(session)
    else:
        session_factory = get_session_factory()
        with session_factory() as new_session:
            _load(new_session)

    total_rules_in_cache = len(RULE_CACHE)
    logger.info(
        f"Finished loading rules from DB. Total rule instances in cache: {total_rules_in_cache}. "
        f"Cache keys (sample): {list(RULE_CACHE.keys())[:5] if total_rules_in_cache > 0 else '[]'}",
    )


async def load_rules_from_file() -> bool:
    """
    Loads rules from the local gzipped JSON file into the cache.
    Used by the slave node.

    支持新的RuleDataSet导出格式，包含向后兼容性处理和增强的错误处理。
    """
    if not os.path.exists(LOCAL_RULES_PATH):
        logger.warning(f"Local rule cache file not found at '{LOCAL_RULES_PATH}'.")
        return False

    # 检查文件基本信息
    try:
        file_size = os.path.getsize(LOCAL_RULES_PATH)
        logger.info(f"Loading rules from local file: {LOCAL_RULES_PATH} (size: {file_size} bytes)")

        if file_size == 0:
            logger.error("Rule cache file is empty")
            return False

    except Exception as e:
        logger.error(f"Failed to check rule cache file: {e}")
        return False

    try:
        rule_registry = RuleRegistry()
        rule_registry.auto_discover_rules()
        all_rule_classes = rule_registry.get_all_rules()

        # 检查文件格式并尝试解压缩
        package_data = None
        file_format = "unknown"

        # 首先检查文件头部判断是否为gzip格式
        with open(LOCAL_RULES_PATH, "rb") as f:
            header = f.read(2)
            is_gzip = header == b"\x1f\x8b"

        if is_gzip:
            # 尝试使用gzip解压缩
            try:
                with gzip.open(LOCAL_RULES_PATH, "rt", encoding="utf-8") as f:
                    package_data = json.load(f)
                file_format = "gzip-compressed JSON"
                logger.info("Successfully loaded gzip-compressed rule data")
            except (OSError, gzip.BadGzipFile) as e:
                logger.error(f"Failed to decompress gzip file: {e}")
                return False
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON from gzip file: {e}")
                return False
        else:
            # 尝试直接读取JSON文件（向后兼容）
            try:
                with open(LOCAL_RULES_PATH, "r", encoding="utf-8") as f:
                    package_data = json.load(f)
                file_format = "plain JSON"
                logger.warning("Loaded plain JSON file (not gzip compressed). Consider updating to compressed format.")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON from plain text file: {e}")
                return False
            except UnicodeDecodeError as e:
                logger.error(f"Failed to decode file as UTF-8: {e}")
                return False

        # 验证和处理数据格式
        rule_datasets_data = None

        if package_data is None:
            logger.error("Failed to load any data from rule cache file")
            return False

        if isinstance(package_data, dict) and "rule_datasets" in package_data:
            # 新格式：{version, rule_datasets: [...], export_timestamp, total_count}
            logger.info(f"Detected new RuleDataSet format (file format: {file_format})")

            # 验证必需字段
            required_fields = ["version", "rule_datasets", "total_count"]
            missing_fields = [field for field in required_fields if field not in package_data]
            if missing_fields:
                logger.error(f"Missing required fields in rule data: {missing_fields}")
                return False

            version = package_data.get("version", "unknown")
            rule_datasets_data = package_data["rule_datasets"]
            expected_count = package_data.get("total_count", 0)
            export_timestamp = package_data.get("export_timestamp", "unknown")

            logger.info(f"Rule package info - Version: {version[:16]}..., Export time: {export_timestamp}")

            # 验证数据完整性
            actual_count = len(rule_datasets_data)
            if expected_count != actual_count:
                logger.warning(
                    f"Data count mismatch: expected {expected_count}, got {actual_count}. "
                    "This may indicate data corruption or incomplete transfer."
                )

            # 验证rule_datasets是列表
            if not isinstance(rule_datasets_data, list):
                logger.error(f"rule_datasets should be a list, got {type(rule_datasets_data)}")
                return False

            # 检测是否为增强格式（包含 RuleDetail 数据）
            enhanced_format = _detect_enhanced_cache_format(rule_datasets_data)
            if enhanced_format:
                logger.info("Detected enhanced cache format with RuleDetail data")
                return _load_from_enhanced_format(rule_datasets_data, all_rule_classes)
            else:
                logger.info("Using standard RuleDataSet format processing")

        elif isinstance(package_data, dict) and "rules" in package_data:
            # 旧格式：{version, rules: {rule_id: {class_path, params}}}
            logger.error(
                f"Detected legacy rule format (file format: {file_format}). "
                "This format is no longer supported. Please update the master node to generate new format."
            )
            return False

        elif isinstance(package_data, list):
            # 直接的RuleDataSet列表格式（向后兼容）
            logger.info(f"Detected direct RuleDataSet list format (file format: {file_format})")
            rule_datasets_data = package_data

        else:
            logger.error(
                f"Unknown rule file format (file format: {file_format}): "
                f"expected dict with 'rule_datasets' or list, got {type(package_data)}"
            )
            # 提供更多调试信息
            if isinstance(package_data, dict):
                logger.error(f"Available keys in data: {list(package_data.keys())}")
            return False

        # 验证rule_datasets_data不为空
        if not rule_datasets_data:
            logger.error("No rule datasets found in the loaded data")
            return False

        logger.info(f"Found {len(rule_datasets_data)} rule datasets to process")

        # 重建RuleDataSet对象
        reconstructed_db_rules = []
        failed_count = 0

        for i, data in enumerate(rule_datasets_data):
            try:
                # 验证数据结构
                if not isinstance(data, dict):
                    logger.error(f"Rule dataset {i} is not a dictionary: {type(data)}")
                    failed_count += 1
                    continue

                # 检查必需字段
                if "id" not in data:
                    logger.error(f"Rule dataset {i} missing 'id' field")
                    failed_count += 1
                    continue

                # 重建为兼容对象格式
                dataset = _reconstruct_dataset_from_cache(data)
                if dataset:
                    reconstructed_db_rules.append(dataset)

            except Exception as e:
                logger.error(f"Failed to reconstruct RuleDataSet {i} (id: {data.get('id', 'unknown')}): {e}")
                logger.debug(f"Problematic data: {data}")
                failed_count += 1
                continue

        # 报告加载结果
        total_count = len(rule_datasets_data)
        success_count = len(reconstructed_db_rules)

        logger.info(f"Rule dataset reconstruction completed: {success_count}/{total_count} successful")

        if failed_count > 0:
            logger.warning(f"{failed_count} rule datasets failed to load and were skipped")

        if not reconstructed_db_rules:
            logger.error("No valid rule datasets could be loaded from file")
            return False

        # 检查加载成功率
        success_rate = success_count / total_count if total_count > 0 else 0
        if success_rate < 0.8:  # 如果成功率低于80%，发出警告
            logger.warning(
                f"Low success rate ({success_rate:.1%}) when loading rule datasets. "
                "This may indicate data corruption or format issues."
            )

        _populate_cache_from_data(reconstructed_db_rules, all_rule_classes)
        logger.info(f"Successfully loaded {len(reconstructed_db_rules)} rule datasets from local file cache.")
        return True

    except Exception as e:
        logger.error(f"Failed to load or parse local rule file: {e}", exc_info=True)
        logger.error(f"File path: {LOCAL_RULES_PATH}")
        logger.error("This error may be caused by:")
        logger.error("1. Corrupted rule cache file")
        logger.error("2. Incompatible file format")
        logger.error("3. Missing file permissions")
        logger.error("4. Insufficient disk space")
        logger.error("Consider deleting the cache file to force a fresh sync from master node.")
        return False
