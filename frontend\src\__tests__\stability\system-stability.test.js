/**
 * 系统稳定性测试
 * 验证长时间运行、并发操作、异常情况处理等稳定性要素
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'

// Store导入
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import { useRulesStore } from '@/stores/rules'

// Composables导入
import { useRuleDetail } from '@/composables/business/useRuleDetail'
import { useRuleDetailsManagement } from '@/composables/business/useRuleDetailsManagement'

// Mock API with error simulation
const createMockApiWithErrors = () => {
  let callCount = 0
  
  return {
    enhancedRuleDetailsApi: {
      getDetailsList: vi.fn().mockImplementation(async () => {
        callCount++
        // 模拟间歇性错误
        if (callCount % 10 === 0) {
          throw new Error('模拟网络错误')
        }
        
        return {
          items: Array.from({ length: 20 }, (_, i) => ({
            id: i + 1,
            rule_name: `规则${i + 1}`,
            status: 'ACTIVE'
          })),
          total: 20
        }
      }),
      getDetailById: vi.fn().mockImplementation(async (ruleKey, id) => {
        if (id === 999) {
          throw new Error('记录不存在')
        }
        return { id, rule_name: `规则${id}`, status: 'ACTIVE' }
      }),
      createDetail: vi.fn().mockImplementation(async (ruleKey, data) => {
        if (!data.rule_name) {
          throw new Error('规则名称不能为空')
        }
        return { id: Date.now(), ...data }
      }),
      updateDetail: vi.fn().mockImplementation(async (ruleKey, id, data) => {
        if (id === 404) {
          throw new Error('记录不存在')
        }
        return { id, ...data }
      }),
      deleteDetail: vi.fn().mockImplementation(async (ruleKey, id) => {
        if (id === 403) {
          throw new Error('无权限删除')
        }
        return true
      }),
      searchDetails: vi.fn().mockResolvedValue({
        items: [],
        total: 0
      }),
      clearCache: vi.fn().mockResolvedValue(true)
    },
    resetCallCount: () => { callCount = 0 }
  }
}

const mockApi = createMockApiWithErrors()

vi.mock('@/api/enhancedRuleDetailsApi', () => mockApi)

// Mock 增强错误处理
const mockErrorHandler = {
  handle: vi.fn(),
  getErrorStats: vi.fn(() => ({
    totalErrors: 0,
    errorTypes: {},
    lastError: null
  }))
}

vi.mock('@/utils/enhancedErrorHandler', () => ({
  enhancedErrorHandler: mockErrorHandler
}))

describe('系统稳定性测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    mockApi.resetCallCount()
  })

  afterEach(() => {
    // 清理定时器
    vi.clearAllTimers()
  })

  describe('长时间运行测试', () => {
    it('应该在长时间运行中保持稳定', async () => {
      const store = useRuleDetailsStore()
      const errors = []
      
      // 模拟长时间运行（100次操作）
      for (let i = 0; i < 100; i++) {
        try {
          await store.fetchDetailsList('test-rule')
          
          // 每10次操作检查一次状态
          if (i % 10 === 0) {
            expect(store.detailsList).toBeDefined()
            expect(Array.isArray(store.detailsList)).toBe(true)
          }
        } catch (error) {
          errors.push(error)
        }
      }
      
      // 验证错误率在可接受范围内（小于10%）
      expect(errors.length).toBeLessThan(10)
      
      // 验证最终状态正常
      expect(store.loading).toBe(false)
      expect(store.detailsList).toBeDefined()
    })

    it('应该正确处理内存管理', async () => {
      const instances = []
      const initialMemory = performance.memory?.usedJSHeapSize || 0
      
      // 创建大量实例
      for (let i = 0; i < 50; i++) {
        const management = useRuleDetailsManagement(`test-rule-${i}`)
        await management.loadDetailsList()
        instances.push(management)
      }
      
      // 清理实例
      instances.forEach(instance => {
        instance.resetPerformanceMetrics?.()
      })
      
      // 强制垃圾回收
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = performance.memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory
      
      // 内存增长应该在合理范围内
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024) // 50MB
    })
  })

  describe('并发操作测试', () => {
    it('应该正确处理并发API调用', async () => {
      const store = useRuleDetailsStore()
      const concurrentOperations = 20
      
      // 创建并发操作
      const promises = Array.from({ length: concurrentOperations }, (_, i) => {
        return store.fetchDetailsList(`test-rule-${i}`)
      })
      
      const startTime = performance.now()
      const results = await Promise.allSettled(promises)
      const endTime = performance.now()
      
      // 验证并发性能
      expect(endTime - startTime).toBeLessThan(1000) // 应在1秒内完成
      
      // 验证成功率
      const successCount = results.filter(r => r.status === 'fulfilled').length
      expect(successCount).toBeGreaterThan(concurrentOperations * 0.8) // 80%成功率
    })

    it('应该正确处理并发CRUD操作', async () => {
      const store = useRuleDetailsStore()
      
      // 并发创建操作
      const createPromises = Array.from({ length: 10 }, (_, i) => 
        store.createDetail('test-rule', { rule_name: `并发规则${i}` })
      )
      
      // 并发读取操作
      const readPromises = Array.from({ length: 10 }, (_, i) => 
        store.fetchDetailById('test-rule', i + 1)
      )
      
      // 并发更新操作
      const updatePromises = Array.from({ length: 5 }, (_, i) => 
        store.updateDetail('test-rule', i + 1, { rule_name: `更新规则${i}` })
      )
      
      const allPromises = [...createPromises, ...readPromises, ...updatePromises]
      const results = await Promise.allSettled(allPromises)
      
      // 验证操作结果
      const successCount = results.filter(r => r.status === 'fulfilled').length
      expect(successCount).toBeGreaterThan(allPromises.length * 0.7) // 70%成功率
    })
  })

  describe('异常情况处理测试', () => {
    it('应该正确处理网络错误', async () => {
      const store = useRuleDetailsStore()
      
      // 模拟网络错误
      mockApi.enhancedRuleDetailsApi.getDetailsList.mockRejectedValueOnce(
        new Error('网络连接超时')
      )
      
      await expect(store.fetchDetailsList('test-rule')).rejects.toThrow('网络连接超时')
      
      // 验证错误后状态恢复
      expect(store.loading).toBe(false)
      
      // 验证后续操作正常
      mockApi.enhancedRuleDetailsApi.getDetailsList.mockResolvedValueOnce({
        items: [],
        total: 0
      })
      
      await expect(store.fetchDetailsList('test-rule')).resolves.toBeDefined()
    })

    it('应该正确处理数据验证错误', async () => {
      const store = useRuleDetailsStore()
      
      // 测试创建时的验证错误
      await expect(
        store.createDetail('test-rule', { rule_name: '' })
      ).rejects.toThrow('规则名称不能为空')
      
      // 测试更新不存在的记录
      await expect(
        store.updateDetail('test-rule', 404, { rule_name: '测试' })
      ).rejects.toThrow('记录不存在')
      
      // 测试删除权限错误
      await expect(
        store.deleteDetail('test-rule', 403)
      ).rejects.toThrow('无权限删除')
    })

    it('应该正确处理组件错误边界', async () => {
      const ruleDetail = useRuleDetail()
      
      // 模拟组件内部错误
      mockApi.enhancedRuleDetailsApi.getDetailById.mockRejectedValueOnce(
        new Error('组件内部错误')
      )
      
      try {
        await ruleDetail.fetchRuleDetail('test-rule')
      } catch (error) {
        // 错误应该被捕获和处理
      }
      
      // 验证错误处理器被调用
      expect(mockErrorHandler.handle).toHaveBeenCalled()
    })
  })

  describe('资源清理测试', () => {
    it('应该正确清理事件监听器', async () => {
      const management = useRuleDetailsManagement('test-rule')
      
      // 模拟添加事件监听器
      const mockEventListener = vi.fn()
      window.addEventListener('beforeunload', mockEventListener)
      
      // 模拟组件卸载
      // 在实际应用中，这会在组件的 onUnmounted 中处理
      window.removeEventListener('beforeunload', mockEventListener)
      
      // 验证事件监听器被清理
      expect(mockEventListener).not.toHaveBeenCalled()
    })

    it('应该正确清理定时器', async () => {
      vi.useFakeTimers()
      
      const management = useRuleDetailsManagement('test-rule')
      
      // 模拟设置定时器
      const timerId = setTimeout(() => {
        management.refreshList?.()
      }, 5000)
      
      // 清理定时器
      clearTimeout(timerId)
      
      // 快进时间
      vi.advanceTimersByTime(6000)
      
      // 验证定时器被清理
      expect(mockApi.enhancedRuleDetailsApi.getDetailsList).not.toHaveBeenCalled()
      
      vi.useRealTimers()
    })
  })

  describe('数据一致性测试', () => {
    it('应该在异常情况下保持数据一致性', async () => {
      const store = useRuleDetailsStore()
      
      // 设置初始数据
      await store.fetchDetailsList('test-rule')
      const initialCount = store.detailsList.length
      
      // 模拟创建失败
      try {
        await store.createDetail('test-rule', { rule_name: '' })
      } catch (error) {
        // 创建失败
      }
      
      // 验证数据没有被污染
      expect(store.detailsList.length).toBe(initialCount)
      
      // 模拟更新失败
      try {
        await store.updateDetail('test-rule', 404, { rule_name: '测试' })
      } catch (error) {
        // 更新失败
      }
      
      // 验证数据保持一致
      expect(store.detailsList.length).toBe(initialCount)
    })

    it('应该正确处理状态回滚', async () => {
      const store = useRuleDetailsStore()
      
      // 设置初始状态
      const initialSelection = [{ id: 1, rule_name: '规则1' }]
      store.setSelectedDetails(initialSelection)
      
      // 模拟操作失败需要回滚
      const originalSelection = [...store.selectedDetails]
      
      try {
        // 模拟批量删除失败
        store.setSelectedDetails([])
        throw new Error('批量删除失败')
      } catch (error) {
        // 回滚选择状态
        store.setSelectedDetails(originalSelection)
      }
      
      // 验证状态正确回滚
      expect(store.selectedDetails).toEqual(initialSelection)
    })
  })

  describe('性能退化测试', () => {
    it('应该在高负载下保持性能', async () => {
      const store = useRuleDetailsStore()
      const responseTimes = []
      
      // 模拟高负载操作
      for (let i = 0; i < 50; i++) {
        const startTime = performance.now()
        
        try {
          await store.fetchDetailsList('test-rule')
        } catch (error) {
          // 忽略错误，专注于性能
        }
        
        const endTime = performance.now()
        responseTimes.push(endTime - startTime)
      }
      
      // 计算平均响应时间
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      
      // 验证性能没有显著退化
      expect(avgResponseTime).toBeLessThan(100) // 平均响应时间应小于100ms
      
      // 验证响应时间稳定性（标准差）
      const variance = responseTimes.reduce((acc, time) => 
        acc + Math.pow(time - avgResponseTime, 2), 0) / responseTimes.length
      const stdDev = Math.sqrt(variance)
      
      expect(stdDev).toBeLessThan(50) // 标准差应小于50ms
    })
  })
})
