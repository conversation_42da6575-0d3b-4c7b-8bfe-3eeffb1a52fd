"""
环境特定的日志配置模块

本模块实现了基于环境的日志配置策略，为不同的运行环境（DEV、TEST、PROD）
提供差异化的日志配置，以提升运维效率和系统安全性。

作者: 系统架构师
创建时间: 2025-07-01
版本: 1.0.0
"""

import re
from abc import ABC, abstractmethod
from typing import Any

from .settings import settings


class SensitiveDataFilter:
    """
    敏感信息过滤器

    用于过滤日志中的敏感信息，如密码、API密钥、个人信息等。
    支持不同环境的过滤策略。
    """

    def __init__(self, environment: str):
        """
        初始化敏感信息过滤器

        Args:
            environment: 运行环境 (DEV, TEST, PROD)
        """
        self.environment = environment.upper()
        self._init_filter_patterns()

    def _init_filter_patterns(self):
        """初始化过滤模式"""
        # 基础敏感信息模式 - 修复正则表达式以正确替换整个键值对
        base_patterns = [
            (r'password["\']?\s*[:=]\s*["\']?[^"\'\\s,}]+', "***FILTERED***"),
            (r'secret["\']?\s*[:=]\s*["\']?[^"\'\\s,}]+', "***FILTERED***"),
            (r'token["\']?\s*[:=]\s*["\']?[^"\'\\s,}]+', "***FILTERED***"),
            (r'key["\']?\s*[:=]\s*["\']?[^"\'\\s,}]+', "***FILTERED***"),
        ]

        # 生产环境额外过滤模式
        prod_patterns = [
            (r'user["\']?\s*[:=]\s*["\']?[^"\'\\s,}]+', "***USER***"),
            (r'email["\']?\s*[:=]\s*["\']?[^"\'\\s,}]+', "***EMAIL***"),
            (r'phone["\']?\s*[:=]\s*["\']?[^"\'\\s,}]+', "***PHONE***"),
            (r'ip["\']?\s*[:=]\s*["\']?[^"\'\\s,}]+', "***IP***"),
        ]

        # 根据环境设置过滤模式
        if self.environment == "DEV":
            self.patterns = []  # 开发环境不过滤
        elif self.environment == "TEST":
            self.patterns = base_patterns  # 测试环境基础过滤
        else:  # PROD
            self.patterns = base_patterns + prod_patterns  # 生产环境完全过滤

    def filter_message(self, message: str) -> str:
        """
        过滤消息中的敏感信息

        Args:
            message: 原始日志消息

        Returns:
            str: 过滤后的消息
        """
        if not self.patterns:
            return message

        filtered_message = message
        for pattern, replacement in self.patterns:
            filtered_message = re.sub(pattern, replacement, filtered_message, flags=re.IGNORECASE)

        return filtered_message


class BaseEnvironmentLogConfig(ABC):
    """
    环境日志配置基类

    定义了所有环境日志配置的通用接口和基础实现。
    子类需要实现环境特定的配置策略。
    """

    def __init__(self, environment: str):
        """
        初始化环境日志配置

        Args:
            environment: 运行环境标识
        """
        self.environment = environment.upper()
        self.sensitive_filter = SensitiveDataFilter(environment)
        self._base_config = self._get_base_config()

    def _get_base_config(self) -> dict[str, Any]:
        """获取基础配置"""
        return {
            "path": settings.LOG_PATH,
            "rotation": settings.LOG_ROTATION,
            "retention": settings.LOG_RETENTION,
            "compression": settings.LOG_COMPRESSION,
            "enqueue": settings.LOG_ENQUEUE,
            "serialize": settings.LOG_SERIALIZE,
        }

    @abstractmethod
    def get_log_level(self) -> str:
        """获取日志级别"""
        pass

    @abstractmethod
    def get_log_format(self) -> str:
        """获取日志格式"""
        pass

    @abstractmethod
    def get_stdout_config(self) -> dict[str, Any]:
        """获取控制台输出配置"""
        pass

    @abstractmethod
    def get_file_config(self) -> dict[str, Any]:
        """获取文件输出配置"""
        pass

    def get_environment_context(self) -> dict[str, Any]:
        """获取环境上下文信息"""
        return {
            "environment": self.environment,
            "mode": settings.MODE,
            "run_mode": settings.RUN_MODE,
        }

    def build_config(self) -> dict[str, Any]:
        """
        构建完整的日志配置

        Returns:
            Dict[str, Any]: 完整的日志配置字典
        """
        config = self._base_config.copy()
        config.update(
            {
                "level": self.get_log_level(),
                "format": self.get_log_format(),
                "stdout_sink": self.get_stdout_config(),
                "file_sink": self.get_file_config(),
                "environment": self.environment,
                "sensitive_filter": self.sensitive_filter,
                "context": self.get_environment_context(),
            }
        )
        return config


class DevLogConfig(BaseEnvironmentLogConfig):
    """
    开发环境日志配置

    特点：
    - DEBUG级别日志
    - 人类可读格式
    - 启用控制台输出
    - 不过滤敏感信息
    - 快速轮转，短保留期
    """

    def get_log_level(self) -> str:
        return getattr(settings, "LOG_DEV_LEVEL", "DEBUG")

    def get_log_format(self) -> str:
        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>[DEV]</cyan> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
            "<level>{message}</level>"
        )

    def get_stdout_config(self) -> dict[str, Any]:
        return {
            "enabled": getattr(settings, "LOG_DEV_STDOUT_ENABLED", True),
            "level": self.get_log_level(),
            "format": (
                "<green>{time:HH:mm:ss}</green> | "
                "<level>{level: <5}</level> | "
                "<cyan>[DEV]</cyan> | "
                "<level>{message}</level>"
            ),
        }

    def get_file_config(self) -> dict[str, Any]:
        return {
            "path": "logs/dev/app_{time}.log",
            "rotation": getattr(settings, "LOG_DEV_ROTATION", "5 MB"),
            "retention": getattr(settings, "LOG_DEV_RETENTION", "3 days"),
            "compression": getattr(settings, "LOG_COMPRESSION", "zip"),
        }


class TestEnvironmentLogConfig(BaseEnvironmentLogConfig):
    """
    测试环境日志配置

    特点：
    - INFO级别日志
    - 结构化格式
    - 禁用控制台输出
    - 基础敏感信息过滤
    - 中等轮转策略
    """

    def get_log_level(self) -> str:
        return getattr(settings, "LOG_TEST_LEVEL", "INFO")

    def get_log_format(self) -> str:
        return (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | " "{level: <8} | " "[TEST] | " "{name}:{function}:{line} - " "{message}"
        )

    def get_stdout_config(self) -> dict[str, Any]:
        return {
            "enabled": getattr(settings, "LOG_TEST_STDOUT_ENABLED", False),
            "level": self.get_log_level(),
            "format": "{time:HH:mm:ss} | {level: <5} | [TEST] | {message}",
        }

    def get_file_config(self) -> dict[str, Any]:
        return {
            "path": "logs/test/app_{time}.log",
            "rotation": getattr(settings, "LOG_TEST_ROTATION", "10 MB"),
            "retention": getattr(settings, "LOG_TEST_RETENTION", "7 days"),
            "compression": getattr(settings, "LOG_COMPRESSION", "zip"),
        }


class ProdLogConfig(BaseEnvironmentLogConfig):
    """
    生产环境日志配置

    特点：
    - WARNING级别日志
    - JSON格式（便于日志分析）
    - 禁用控制台输出
    - 完全敏感信息过滤
    - 大文件，长保留期
    """

    def get_log_level(self) -> str:
        return getattr(settings, "LOG_PROD_LEVEL", "WARNING")

    def get_log_format(self) -> str:
        # 生产环境使用结构化格式，便于日志聚合和分析
        # 避免JSON格式中的引号冲突问题
        return "{time:YYYY-MM-DD HH:mm:ss.SSS} | " "{level: <8} | " "PROD | " "{name}:{function}:{line} | " "{message}"

    def get_stdout_config(self) -> dict[str, Any]:
        return {
            "enabled": getattr(settings, "LOG_PROD_STDOUT_ENABLED", False),
            "level": self.get_log_level(),
            "format": "{time:HH:mm:ss} | {level: <5} | PROD | {message}",
        }

    def get_file_config(self) -> dict[str, Any]:
        return {
            "path": "logs/prod/app_{time}.log",
            "rotation": getattr(settings, "LOG_PROD_ROTATION", "50 MB"),
            "retention": getattr(settings, "LOG_PROD_RETENTION", "30 days"),
            "compression": getattr(settings, "LOG_PROD_COMPRESSION", "gz"),  # 生产环境使用更高效的压缩
        }


class LogConfigFactory:
    """
    日志配置工厂类

    根据运行环境创建相应的日志配置实例。
    支持环境自动检测和手动指定。
    """

    _config_classes = {
        "DEV": DevLogConfig,
        "TEST": TestEnvironmentLogConfig,
        "PROD": ProdLogConfig,
    }

    @classmethod
    def create_config(cls, environment: str | None = None) -> BaseEnvironmentLogConfig:
        """
        创建环境特定的日志配置

        Args:
            environment: 指定环境，如果为None则自动检测

        Returns:
            BaseEnvironmentLogConfig: 环境特定的日志配置实例

        Raises:
            ValueError: 不支持的环境类型
        """
        if environment is None:
            environment = cls._detect_environment()

        environment = environment.upper()

        if environment not in cls._config_classes:
            raise ValueError(f"不支持的环境类型: {environment}. 支持的环境: {list(cls._config_classes.keys())}")

        config_class = cls._config_classes[environment]
        return config_class(environment)

    @classmethod
    def _detect_environment(cls) -> str:
        """
        自动检测运行环境

        Returns:
            str: 检测到的环境标识
        """
        run_mode = settings.RUN_MODE.upper()

        # 环境映射规则
        if run_mode in ["DEV", "DEVELOPMENT"]:
            return "DEV"
        elif run_mode in ["TEST", "TESTING"]:
            return "TEST"
        elif run_mode in ["PROD", "PRODUCTION"]:
            return "PROD"
        else:
            # 默认为生产环境，确保安全
            return "PROD"

    @classmethod
    def get_supported_environments(cls) -> list[str]:
        """
        获取支持的环境列表

        Returns:
            List[str]: 支持的环境列表
        """
        return list(cls._config_classes.keys())


# 便捷函数
def get_environment_log_config(environment: str | None = None) -> BaseEnvironmentLogConfig:
    """
    获取环境特定的日志配置

    Args:
        environment: 指定环境，如果为None则自动检测

    Returns:
        BaseEnvironmentLogConfig: 环境特定的日志配置实例
    """
    return LogConfigFactory.create_config(environment)


def get_current_log_config() -> BaseEnvironmentLogConfig:
    """
    获取当前环境的日志配置

    Returns:
        BaseEnvironmentLogConfig: 当前环境的日志配置实例
    """
    return LogConfigFactory.create_config(None)
